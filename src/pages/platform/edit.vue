<template>
  <div class="modern-form-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">编辑平台</h1>
    </div>
    <t-form
      :data="formData"
      @reset="onReset"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
      <!-- 第一步：基础信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基础信息</h3>
          <p class="section-desc">设置平台的基本信息</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">平台名称</label>
              <t-input
                v-model="formData.app_name"
                placeholder="请输入公众号/小程序名称"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">平台描述</label>
              <t-input
                v-model="formData.desc"
                placeholder="请输入平台描述信息"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">AppId</label>
              <t-input
                v-model="formData.app_id"
                placeholder="请输入微信AppId"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">AppSecret</label>
              <t-input
                v-model="formData.app_secret"
                placeholder="请输入微信AppSecret"
                class="modern-input"
                type="password"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 第二步：配置设置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">配置设置</h3>
          <p class="section-desc">设置平台的配置信息</p>
        </div>

        <div class="form-grid">
          <template v-if="$store.state.user.admin==1">
            <div class="form-group">
              <div class="floating-input">
                <label class="floating-label">平台使用者</label>
                <t-select
                  v-model="formData.selectValue"
                  placeholder="请选择平台使用者"
                  class="modern-select"
                >
                  <t-option
                    v-for="item in userList"
                    :value="item.id"
                    :label="item.name"
                    :key="item.id"
                  ></t-option>
                </t-select>
              </div>
            </div>

            <div class="form-group">
              <div class="floating-input">
                <label class="floating-label">平台过期时间</label>
                <t-date-picker
                  :clearable="true"
                  placeholder="请选择平台过期时间"
                  :enableTimePicker="true"
                  :allow-input="false"
                  v-model="formData.days"
                  class="modern-datepicker"
                />
              </div>
            </div>
          </template>

          <div class="form-group" v-if="is==1">
            <div class="floating-input">
              <label class="floating-label">平台域名</label>
              <t-input
                v-model="formData.pigeno_url"
                placeholder="请输入平台域名"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group full-width" style="padding-left: 15px;">
            <label class="floating-label">平台图标</label>
            <div class="upload-area">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{token:$store.state.user.token}"
                v-model="formData.img_icon"
                theme="image"
                tips="拖拽或点击上传平台图标"
                accept="image/*"
                class="modern-upload"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button
          theme="default"
          @click="()=>{this.$router.push('/platform/index')}"
          class="action-btn back-btn"
          size="large"
        >
          <span>返回</span>
        </t-button>
        <t-button
          theme="primary"
          type="submit"
          class="action-btn submit-btn"
          size="large"
        >
          <span>保存修改</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
export default {

  data() {
    return {
      formData: {
        days:'',
        selectValue: '',
        img_icon: [],
        app_name:'',
        app_id:'',
        app_secret:'',
        pigeno_url:'',
        desc:'',
      },
      showMode: false,
      list: [],
      userList: [],
      plaId: 0,
      is:0
    };
  },
  mounted() {
    this.plaId = this.$route.query.id;
    this.getUserList();
    this.getPlatformInfo();
    this.getUserIs();
  },
  methods: {
    getUserIs(){
      this.$request
        .post("/Platform/getUserIs")
        .then((res) => {
          this.is=res;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    dianType(d) {
      this.showMode = true;
      this.$request
        .post("/Platform/addPlatform", {type: d})
        .then((res) => {
          this.list = res.info;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getPlatformInfo() {
      this.$request
        .post("/Platform/getPlatformInfo", {id: this.plaId})
        .then((res) => {
          if (res.code == 200) {
            this.formData.selectValue=res.info.user_id;
            this.formData.app_name=res.info.platform_name;
            this.formData.desc=res.info.content;
            this.formData.app_id=res.info.app_id;
            this.formData.app_secret=res.info.app_secret;
            this.formData.days=res.info.overdue;
            this.formData.pigeno_url=res.info.pigeno_url;
            this.formData.img_icon=[{url:res.info.platform_img}];
          } else {

            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getUserList() {
      this.$request
        .post("/user/getUserMod",)
        .then((res) => {
          this.userList = res.data;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Platform/editPlatformFrom", {from_data: this.formData,id:this.plaId})
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg);
              setTimeout(() => {
                this.$router.push('index');
              }, 1500)
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    onReset() {

    },
    modulesChange(d) {
      if (d.length == 0) {
        this.checkList = [];
      } else {
        var arr = [];
        d.forEach(i => {
          const result = this.list.find(item => item.value === i);
          arr.push(result);
        });
        this.checkList = arr;
      }
    },
  },

};
</script>
<style lang="less">
// ==================== 现代化SaaS设计变量 ====================
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #f3f4f6;
  --primary-lighter: #f8f9fa;
  --primary-border: #e9ecef;
  --primary-border-hover: #d1d5db;
  --success-color: #10b981;
  --success-dark: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-form: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器现代化
.modern-form-container {
  min-height: 100vh !important;
  background: var(--background-primary);
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域现代化
.page-header {
  text-align: center !important;
  margin-bottom: 32px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 0 8px 0 !important;
    letter-spacing: -0.025em !important;

    @media (max-width: 768px) {
      font-size: 1.5rem !important;
    }
  }

  .page-subtitle {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块现代化
.form-section {
  background: var(--background-primary) !important;
  border-radius: var(--radius-large) !important;
  padding: 32px !important;
  margin-bottom: 24px !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  position: relative !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0.75%;
    right: 0.75%;
    height: 2px;
    width: 98.5%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-medium) var(--radius-medium) 0 0;
  }

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-heavy) !important;
    border-color: var(--primary-border-hover) !important;

    &::before {
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    padding: 20px !important;
  }
}

// 区块标题现代化
.section-header {
  margin-bottom: 24px;
  text-align: center;

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    letter-spacing: -0.025em;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// 表单网格优化
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  align-items: start;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 表单组优化
.form-group {
  position: relative;
  width: 100%;
  margin-bottom: 20px;

  &.full-width {
    grid-column: 1 / -1;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
}

// 简化输入框设计
.floating-input {
  position: relative;
  margin-bottom: 0;
  width: 100%;

  .floating-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
  }

  // .modern-input 样式通过 :deep(.t-input) 覆盖，避免样式冲突
  .modern-input {
    width: 100%;
    // 其他样式通过 :deep(.t-input) 统一控制
  }

  // TDesign组件样式通过:deep()覆盖，不在这里定义
  .modern-select,
  .modern-datepicker {
    width: 100%;
  }

  .input-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1rem;
    color: var(--text-secondary);
    transition: var(--transition);
    z-index: 5;
  }

  &:focus-within .input-icon {
    color: var(--primary-color);
  }
}

// 上传区域
.upload-area {
  margin-top: 15px;

  .modern-upload {
    .t-upload__dragger {
      border: 2px dashed var(--border-color) !important;
      border-radius: var(--radius-medium) !important;
      background: var(--background-primary) !important;
      padding: 40px !important;
      transition: var(--transition) !important;

      &:hover {
        border-color: var(--primary-color) !important;
        background: var(--primary-light) !important;
      }
    }
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em !important;

  .btn-icon {
    font-size: 1rem;
  }

  &.submit-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;

    &:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }

  &.back-btn {
    background: #ffffff !important;
    color: var(--text-primary) !important;
    border: 1.5px solid #d1d5db !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

    &:hover {
      background: #f9fafb !important;
      color: var(--text-primary) !important;
      border-color: #9ca3af !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }

    &:active {
      transform: translateY(0) !important;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式现代化覆盖
:deep(.t-input) {
  border-radius: var(--radius-small) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  background: var(--background-primary) !important;
  box-shadow: var(--shadow-light) !important;
  height: 48px !important;
  font-size: 0.875rem !important;
  color: var(--text-primary) !important;

  &::placeholder {
    color: var(--text-secondary) !important;
    font-weight: 400 !important;
  }

  &:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  &:hover {
    border-color: var(--primary-border-hover) !important;
  }

  .t-input__inner {
    height: 46px !important;
    line-height: 46px !important;
    color: var(--text-primary) !important;

    &::placeholder {
      color: var(--text-secondary) !important;
      font-weight: 400 !important;
    }
  }
}

:deep(.t-select) {
  .t-input {
    border-radius: var(--radius-small) !important;
    border: 1px solid var(--border-color) !important;
    transition: var(--transition) !important;
    background: var(--background-primary) !important;
    box-shadow: var(--shadow-light) !important;
    height: 48px !important;
    font-size: 0.875rem !important;

    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }

    &:hover {
      border-color: var(--primary-border-hover) !important;
    }

    .t-input__inner {
      height: 46px !important;
      line-height: 46px !important;
    }
  }
}

:deep(.t-date-picker) {
  .t-input {
    border-radius: var(--radius-small) !important;
    border: 1px solid var(--border-color) !important;
    transition: var(--transition) !important;
    background: var(--background-primary) !important;
    box-shadow: var(--shadow-light) !important;
    height: 48px !important;
    font-size: 0.875rem !important;

    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }

    &:hover {
      border-color: var(--primary-border-hover) !important;
    }

    .t-input__inner {
      height: 46px !important;
      line-height: 46px !important;
    }
  }
}

:deep(.t-upload) {
  .t-upload__dragger {
    border: 2px dashed var(--border-color) !important;
    border-radius: var(--radius-medium) !important;
    background: var(--primary-lighter) !important;
    transition: var(--transition) !important;

    &:hover {
      border-color: var(--primary-color) !important;
      background: var(--primary-light) !important;
    }
  }
}
</style>
