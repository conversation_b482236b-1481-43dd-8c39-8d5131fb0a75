<template>
  <div class="modern-form-container">
    <t-form
      :data="formData"
      @reset="onReset"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
      <!-- 第一步：基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <p class="section-desc">设置网站的基本信息和备案信息</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">网站名称</label>
              <t-input
                v-model="formData.site_name"
                placeholder="请输入网站名称"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">备案号</label>
              <t-input
                v-model="formData.record"
                placeholder="请填写备案号"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">备案链接</label>
              <t-input
                v-model="formData.record_url"
                placeholder="请填写备案链接"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">站点版权</label>
              <t-input
                v-model="formData.copyright"
                placeholder="请填写站点版权信息"
                class="modern-input"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 第二步：视觉设置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">视觉设置</h3>
          <p class="section-desc">配置网站的视觉样式和颜色</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label class="modern-label">&nbsp;版权字体颜色</label>
            <div class="color-picker-container">
              <t-color-picker
                v-model="formData.text_color"
                format="HEX"
                :color-modes="['monochrome']"
                class="modern-color-picker"
              />
              <div class="color-preview" :style="{ backgroundColor: formData.text_color }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第三步：图片配置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">图片配置</h3>
          <p class="section-desc">上传网站LOGO和登录背景图</p>
        </div>

        <div class="image-grid">
          <div class="image-group">
            <div class="custom-upload-container">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{token:$store.state.user.token}"
                v-model="formData.site_img"
                theme="custom"
                accept="image/*"
                :multiple="false"
                class="custom-upload"
              >
                <div class="upload-area logo-area">
                  <div v-if="!formData.site_img || formData.site_img.length === 0" class="upload-placeholder">
                    <div class="upload-icon">🏢</div>
                    <div class="upload-text">点击上传LOGO</div>
                    <div class="upload-hint">建议尺寸：200x60px</div>
                  </div>
                  <div v-else class="upload-preview">
                    <img :src="formData.site_img[0].url" alt="站点LOGO" class="preview-image">
                    <div class="upload-overlay">
                      <div class="overlay-text">点击重新上传</div>
                    </div>
                  </div>
                </div>
              </t-upload>
            </div>
          </div>

          <div class="image-group">
            <div class="custom-upload-container">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{token:$store.state.user.token}"
                v-model="formData.login_bg"
                theme="custom"
                accept="image/*"
                :multiple="false"
                class="custom-upload"
              >
                <div class="upload-area bg-area">
                  <div v-if="!formData.login_bg || formData.login_bg.length === 0" class="upload-placeholder">
                    <div class="upload-icon">🖼️</div>
                    <div class="upload-text">点击上传背景图</div>
                    <div class="upload-hint">建议尺寸：1920x1080px</div>
                  </div>
                  <div v-else class="upload-preview">
                    <img :src="formData.login_bg[0].url" alt="登录背景图" class="preview-image">
                    <div class="upload-overlay">
                      <div class="overlay-text">点击重新上传</div>
                    </div>
                  </div>
                </div>
              </t-upload>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button
          theme="primary"
          type="submit"
          class="action-btn submit-btn"
          size="large"
        >
          <span>保存设置</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formData: {
        site_img: [],
        login_bg: [],
        site_name:'',
        copyright:'',
        record_url:'',
        record:'',
        text_color:'',
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.$request
        .post("/Settings/getIndex",)
        .then((res) => {
          this.formData.site_name=res.info.site_name;
          this.formData.copyright=res.info.copyright;
          this.formData.record_url=res.info.record_url;
          this.formData.record=res.info.record;
          this.formData.text_color=res.info.text_color;
          this.formData.site_img=[{url:res.info.site_img}];
          this.formData.login_bg=[{url:res.info.login_bg}];
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Settings/index",{from_data:this.formData})
          .then((res) => {
            if(res.code==200){
              this.getList();
              this.$message.success(res.msg);
            }else{
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    onReset() {

    },
  },

};
</script>
<style lang="less">
// ==================== 现代化SaaS设计变量 ====================
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #f3f4f6;
  --primary-lighter: #f8f9fa;
  --primary-border: #e9ecef;
  --primary-border-hover: #d1d5db;
  --success-color: #10b981;
  --success-dark: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-form: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器现代化
.modern-form-container {
  min-height: 100vh !important;
  background: var(--background-primary);
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域现代化
.page-header {
  text-align: center !important;
  margin-bottom: 32px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 0 8px 0 !important;
    letter-spacing: -0.025em !important;

    @media (max-width: 768px) {
      font-size: 1.5rem !important;
    }
  }

  .page-subtitle {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块现代化
.form-section {
  background: var(--background-primary) !important;
  border-radius: var(--radius-large) !important;
  padding: 32px !important;
  margin-bottom: 24px !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  position: relative !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0.75%;
    right: 0.75%;
    height: 2px;
    width: 98.5%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-medium) var(--radius-medium) 0 0;
  }

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-heavy) !important;
    border-color: var(--primary-border-hover) !important;

    &::before {
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    padding: 20px !important;
  }
}

// 区块标题现代化
.section-header {
  margin-bottom: 24px;
  text-align: center;

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    letter-spacing: -0.025em;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// 表单网格优化
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  align-items: start;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 表单组优化
.form-group {
  position: relative;
  width: 100%;
  margin-bottom: 20px;

  &.full-width {
    grid-column: 1 / -1;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 0.95rem;
}

// 简化输入框设计
.floating-input {
  position: relative;
  margin-bottom: 0;
  width: 100%;

  .floating-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
  }

  .modern-input {
    width: 100%;
  }
}

// 颜色选择器容器
.color-picker-container {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: #ffffff;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-small);
  transition: var(--transition);

  &:hover {
    border-color: var(--primary-border-hover);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
  }

  .modern-color-picker {
    flex: 1;
  }

  .color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 图片网格
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 25px;
  }
}

// 图片组
.image-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  .modern-label {
    margin-bottom: 15px;
    text-align: center;
  }
}

// 自定义上传容器
.custom-upload-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 15px;

  .custom-upload {
    width: 300px;
    height: auto;
  }
}

// 上传区域
.upload-area {
  border: 2px dashed #e3f2fd;
  border-radius: 15px;
  background: #f8fbff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &.logo-area {
    width: 300px !important;
    height: 150px !important;
    min-width: 300px !important;
    min-height: 150px !important;
    max-width: 300px !important;
    max-height: 150px !important;
  }

  &.bg-area {
    width: 300px !important;
    height: 200px !important;
    min-width: 300px !important;
    min-height: 200px !important;
    max-width: 300px !important;
    max-height: 200px !important;
  }

  &:hover {
    border-color: #2196F3;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.15);
  }
}

// 上传占位符
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 20px;

  .upload-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #2196F3;
  }

  .upload-text {
    font-size: 1rem;
    font-weight: 600;
    color: #1976D2;
    margin-bottom: 8px;
  }

  .upload-hint {
    font-size: 0.8rem;
    color: #666;
  }
}

// 上传预览
.upload-preview {
  position: relative;
  width: 100%;
  height: 100%;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 13px;
  }

  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(33, 150, 243, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 13px;

    .overlay-text {
      color: white;
      font-weight: 600;
      font-size: 0.9rem;
    }
  }

  &:hover .upload-overlay {
    opacity: 1;
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em !important;

  .btn-icon {
    font-size: 1rem;
  }

  &.submit-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;

    &:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// TDesign组件样式现代化覆盖
:deep(.t-input) {
  border-radius: var(--radius-small) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  background: var(--background-primary) !important;
  box-shadow: var(--shadow-light) !important;
  height: 48px !important;
  font-size: 0.875rem !important;
  color: var(--text-primary) !important;

  &::placeholder {
    color: var(--text-secondary) !important;
    font-weight: 400 !important;
  }

  &:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  &:hover {
    border-color: var(--primary-border-hover) !important;
  }

  .t-input__inner {
    height: 46px !important;
    line-height: 46px !important;
    color: var(--text-primary) !important;

    &::placeholder {
      color: var(--text-secondary) !important;
      font-weight: 400 !important;
    }
  }
}

:deep(.t-upload) {
  .t-upload__dragger {
    border-radius: var(--radius-medium) !important;
    border: 2px dashed var(--border-color) !important;
    transition: var(--transition) !important;
    background: var(--primary-lighter) !important;

    &:hover {
      border-color: var(--primary-color) !important;
      background: var(--primary-light) !important;
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .image-grid {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-color-picker) {
  border-radius: 8px !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: scale(1.05) !important;
  }
}

// 上传组件固定大小
:deep(.t-upload) {
  width: 100% !important;

  .t-upload__dragger {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .t-upload__file-list {
    display: none !important;
  }

  .t-upload__tips {
    display: none !important;
  }
}
</style>
