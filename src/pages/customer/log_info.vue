<template>
  <div class="modern-list-container">
    <div class="search-section" v-if="$store.state.user.admin==1">
      <div class="search-area">
        <div class="search-input-group">
          <t-input
            v-model="searchName"
            placeholder="请输入用户名称进行搜索..."
            size="large"
            class="search-input"
            @enter="searchList"
          >
            <template #prefix-icon>
              <span class="search-icon"></span>
            </template>
          </t-input>
          <t-button
            theme="primary"
            size="large"
            @click="searchList"
            class="search-btn"
          >
            <span class="btn-icon"></span>
            <span>查询</span>
          </t-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="modern-table-container">
        <t-table
          rowKey="index"
          :data="list"
          :columns="columns"
          :stripe="true"
          :bordered="false"
          :hover="true"
          size="large"
          table-layout="auto"
          :pagination="pagination"
          :showHeader="true"
          cellEmptyContent="-"
          class="modern-table"
        ></t-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <t-pagination
        :total="pagination.total"
        :page-size="10"
        @current-change="onCurrentChange"
        :showPageSize="false"
        class="modern-pagination"
      ></t-pagination>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      list: [],
      searchName: '',
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
      columns: [
        {colKey: 'user_name', title: '用户信息'},
        {colKey: 'visit_ip', title: '用户IP'},
        {colKey: 'user_agent', title: '用户浏览器标识',width:'800px'},
        {colKey: 'annotate', title: '说明'},
        {colKey: 'operating_time', title: '访问时间'},
      ]
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    searchList() {
      this.pagination.page = 1;
      this.getList();
    },
    onCurrentChange(d) {
      this.pagination.page = d;
      this.getList();
    },
    getList() {
      this.$request
        .post("/Settings/log_list", {search: this.searchName, page: this.pagination.page, limit: this.pagination.size})
        .then((res) => {
          this.list = res.data;
          this.pagination.total = res.count;
        })
        .catch((e) => {
          console.log(e);
        });
    },

    onReset() {

    },
  },

};
</script>
<style lang="less">
// ==================== 现代化SaaS设计变量 ====================
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #f3f4f6;
  --primary-lighter: #f8f9fa;
  --primary-border: #e9ecef;
  --primary-border-hover: #d1d5db;
  --success-color: #10b981;
  --success-dark: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-form: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

<style lang="less" scoped>
// ==================== 现代化列表页面设计系统 ====================

// 页面容器
.modern-list-container {
  min-height: 100vh !important;
  background: var(--background-primary);
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域现代化
.page-header {
  text-align: center !important;
  margin-bottom: 32px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 0 8px 0 !important;
    letter-spacing: -0.025em !important;

    @media (max-width: 768px) {
      font-size: 1.5rem !important;
    }
  }

  .page-subtitle {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 搜索区域现代化
.search-section {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  transition: var(--transition);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);
  }
}

.search-area {
  display: flex;
  align-items: center;

  .search-input-group {
    display: flex;
    gap: 15px;
    align-items: center;
    max-width: 500px;
    width: 100%;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 15px;
    }
  }

  .search-input {
    flex: 1;
    min-width: 300px;

    @media (max-width: 768px) {
      min-width: 100%;
    }
  }

  .search-icon {
    font-size: 1.1rem;
    color: var(--text-secondary);
  }

  .search-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    border-radius: var(--radius-medium) !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
    transition: var(--transition) !important;
    min-width: 100px;
    letter-spacing: 0.025em !important;

    &:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }

    .btn-icon {
      font-size: 1rem;
    }
  }
}

// 表格区域现代化
.table-section {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  transition: var(--transition);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);
  }
}

// 现代化表格容器
.modern-table-container {
  .modern-table {
    border-radius: 12px;
    overflow: hidden;
  }
}

// 分页区域现代化
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 20px 32px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  transition: var(--transition);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .pagination-info {
    .info-text {
      color: #666;
      font-size: 0.9rem;
      font-weight: 500;
    }
  }

  .modern-pagination {
    display: flex;
    align-items: center;
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

.search-section {
  animation: slideInUp 0.6s ease-out 0.1s both;
}

.table-section {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.pagination-section {
  animation: slideInUp 0.6s ease-out 0.3s both;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-list-container {
    padding: 20px 10px;
  }
}

// TDesign组件样式现代化覆盖
:deep(.t-input) {
  border-radius: var(--radius-small) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  background: var(--background-primary) !important;
  box-shadow: var(--shadow-light) !important;
  height: 40px !important;
  font-size: 0.875rem !important;
  color: var(--text-primary) !important;

  &::placeholder {
    color: var(--text-secondary) !important;
    font-weight: 400 !important;
  }

  &:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  &:hover {
    border-color: var(--primary-border-hover) !important;
  }

  .t-input__inner {
    height: 38px !important;
    line-height: 38px !important;
    color: var(--text-primary) !important;

    &::placeholder {
      color: var(--text-secondary) !important;
      font-weight: 400 !important;
    }
  }
}

:deep(.t-table) {
  border-radius: var(--radius-medium) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-color) !important;
  background: var(--background-primary) !important;

  .t-table__header {
    background: var(--primary-lighter) !important;

    th {
      background: var(--primary-lighter) !important;
      color: var(--text-primary) !important;
      font-weight: 600 !important;
      font-size: 0.875rem !important;
      border-bottom: 1px solid var(--border-color) !important;
      padding: 16px 12px !important;
    }
  }

  .t-table__body {
    tr {
      transition: var(--transition) !important;
      border-bottom: 1px solid var(--border-color) !important;

      &:hover {
        background: var(--primary-lighter) !important;
        transform: translateY(-1px) !important;
        box-shadow: var(--shadow-light) !important;
      }

      &.t-table__row--striped {
        background: var(--primary-lighter) !important;

        &:hover {
          background: var(--primary-light) !important;
        }
      }
    }

    td {
      border-bottom: 1px solid var(--border-color) !important;
      padding: 16px 12px !important;
      font-size: 0.875rem !important;
      color: var(--text-primary) !important;
    }
  }
}

:deep(.t-pagination) {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;

  .t-pagination__total {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
  }

  .t-pagination__prev,
  .t-pagination__next,
  .t-pagination__item {
    border-radius: var(--radius-small) !important;
    transition: var(--transition) !important;
    border: 1px solid var(--border-color) !important;
    background: var(--background-primary) !important;
    color: var(--text-primary) !important;
    font-weight: 500 !important;

    &:hover {
      background: var(--primary-color) !important;
      color: white !important;
      border-color: var(--primary-color) !important;
      transform: translateY(-1px) !important;
      box-shadow: var(--shadow-light) !important;
    }

    &.t-is-current {
      background: var(--primary-color) !important;
      color: white !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
    }
  }
}
</style>
