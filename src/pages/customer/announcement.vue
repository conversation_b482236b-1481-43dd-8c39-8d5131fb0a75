<template>
  <div class="modern-list-container">
    <!-- 操作区域 -->
    <div class="action-section">
      <div class="search-area">
        <div class="search-input-group">
          <t-input
            v-model="searchKeyword"
            placeholder="搜索公告标题或内容..."
            size="large"
            class="search-input"
            @enter="handleSearch"
          >
            <template #prefix-icon>
              <span class="search-icon"></span>
            </template>
          </t-input>
          <t-button
            theme="primary"
            size="large"
            @click="handleSearch"
            class="search-btn"
          >
            搜索
          </t-button>
        </div>
      </div>

      <div class="action-buttons">
        <t-button
          v-if="$store.state.user.admin==1"
          @click="openUrl('add_announcement')"
          theme="primary"
          size="large"
          class="create-btn"
        >
          <span>新建公告</span>
        </t-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="modern-table-container">
        <t-table
          rowKey="index"
          :data="filteredList"
          :columns="columns"
          :stripe="true"
          :bordered="false"
          :hover="true"
          size="large"
          table-layout="auto"
          :showHeader="true"
          cellEmptyContent="-"
          class="modern-table"
        >
          <template #op="{ row }">
            <div class="action-buttons-group">
              <t-button
                variant="outline"
                theme="primary"
                size="small"
                @click="openUrl('info?id='+row.id)"
                class="view-btn"
              >
                <span>查看</span>
              </t-button>
              <template v-if="$store.state.user.admin==1">
                <t-button
                  variant="outline"
                  theme="warning"
                  size="small"
                  @click="openUrl('edit_announcement?id='+row.id)"
                  class="edit-btn"
                >
                  <span>编辑</span>
                </t-button>
                <t-popconfirm content="确认删除这条公告吗？" @confirm="del(row.id)">
                  <t-button
                    variant="outline"
                    theme="danger"
                    size="small"
                    class="delete-btn"
                  >
                    <span>删除</span>
                  </t-button>
                </t-popconfirm>
              </template>
            </div>
          </template>
        </t-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <t-pagination
        :total="pagination.total"
        :page-size="10"
        @current-change="onCurrentChange"
        :showPageSize="false"
        class="modern-pagination"
      ></t-pagination>
    </div>
  </div>
</template>
<script>

export default {
  data() {
    return {
      visible:false,
      list:[],
      searchKeyword: '',
      pagination: {
        page: 1,
        size:10,
        total:0,
      },
      columns:[
        { colKey: 'title', title: '标题'},
        { colKey: 'content', title: '内容'},
        { colKey: 'add_time', title: '发布时间'},
        { colKey: 'op', title: '操作'},
      ]
    };
  },
  computed: {
    filteredList() {
      if (!this.searchKeyword) {
        return this.list;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return this.list.filter(item => {
        return item.title.toLowerCase().includes(keyword) ||
               (item.content && item.content.toLowerCase().includes(keyword));
      });
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleSearch() {
      // 搜索功能已通过computed属性filteredList实现
      // 这里可以添加额外的搜索逻辑，比如高亮显示等
      console.log('搜索关键词:', this.searchKeyword);
    },
    onCurrentChange(d){
      this.pagination.page=d;
      this.getList();
    },
    del(d){
      this.$request
        .post("/Settings/DelAbout",{id:d})
        .then((res) => {
          if(res.code===200){
            this.$message.success(res.msg);
            this.getList();
          }else{
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getList() {
      this.$request
        .post("/Settings/about_list",{page:this.pagination.page,limit:this.pagination.size})
        .then((res) => {
          this.list = res.data;
          this.pagination.total=res.count;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    openUrl(url){
      this.$router.push(url);
    }
  },

};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style lang="less">
// ==================== 现代化SaaS设计变量 ====================
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #f3f4f6;
  --primary-lighter: #f8f9fa;
  --primary-border: #e9ecef;
  --primary-border-hover: #d1d5db;
  --success-color: #10b981;
  --success-dark: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-form: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

<style lang="less" scoped>
// ==================== 现代化列表页面设计系统 ====================

// 页面容器现代化
.modern-list-container {
  min-height: 100vh !important;
  background: var(--background-primary);
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域现代化
.page-header {
  text-align: center !important;
  margin-bottom: 32px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 0 8px 0 !important;
    letter-spacing: -0.025em !important;

    @media (max-width: 768px) {
      font-size: 1.5rem !important;
    }
  }

  .page-subtitle {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 操作区域现代化
.action-section {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  transition: var(--transition);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }
}

// 搜索区域
.search-area {
  flex: 1;
  max-width: 500px;

  .search-input-group {
    display: flex;
    gap: 15px;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;
    }
  }

  .search-input {
    flex: 1;
    min-width: 300px;

    @media (max-width: 768px) {
      min-width: 100%;
    }
  }

  .search-icon {
    font-size: 1.1rem;
    color: var(--text-secondary);
  }

  .search-btn {
    min-width: 80px;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: var(--radius-small) !important;
    padding: 8px 16px !important;
    font-size: 0.875rem !important;
    transition: var(--transition) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;

    &:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }
  }
}

// 操作按钮区域
.action-buttons {
  display: flex;
  gap: 15px;

  .create-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    border-radius: var(--radius-medium) !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
    transition: var(--transition) !important;
    letter-spacing: 0.025em !important;

    &:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }

    .btn-icon {
      font-size: 1rem;
    }
  }
}

// 表格区域现代化
.table-section {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  transition: var(--transition);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);
  }
}

// 现代化表格容器
.modern-table-container {
  .modern-table {
    border-radius: 12px;
    overflow: hidden;
  }
}

// 操作按钮组
.action-buttons-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .view-btn,
  .edit-btn,
  .delete-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px !important;
    border-radius: var(--radius-small) !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    transition: var(--transition) !important;
    min-width: 60px;
    justify-content: center;
    letter-spacing: 0.025em !important;

    .btn-icon {
      font-size: 0.875rem;
    }

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: var(--shadow-light) !important;
    }
  }

  .view-btn {
    border: 1px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
    background: var(--background-primary) !important;

    &:hover {
      background: var(--primary-color) !important;
      color: white !important;
    }
  }

  .edit-btn {
    border: 1px solid var(--warning-color) !important;
    color: var(--warning-color) !important;
    background: var(--background-primary) !important;

    &:hover {
      background: var(--warning-color) !important;
      color: white !important;
    }
  }

  .delete-btn {
    border: 1px solid var(--error-color) !important;
    color: var(--error-color) !important;
    background: var(--background-primary) !important;

    &:hover {
      background: var(--error-color) !important;
      color: white !important;
    }
  }
}

// 分页区域现代化
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 20px 32px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  transition: var(--transition);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .pagination-info {
    .info-text {
      color: #666;
      font-size: 0.9rem;
      font-weight: 500;
    }
  }

  .modern-pagination {
    display: flex;
    align-items: center;
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

.action-section {
  animation: slideInUp 0.6s ease-out 0.1s both;
}

.table-section {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.pagination-section {
  animation: slideInUp 0.6s ease-out 0.3s both;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-list-container {
    padding: 20px 10px;
  }

  .action-buttons-group {
    justify-content: center;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-table) {
  border-radius: 12px !important;
  overflow: hidden !important;

  .t-table__header {
    background: var(--primary-lighter) !important;

    th {
      background: var(--primary-lighter) !important;
      color: var(--text-primary) !important;
      font-weight: 600 !important;
      font-size: 0.875rem !important;
      border-bottom: 1px solid var(--border-color) !important;
      padding: 16px 12px !important;
    }
  }

  .t-table__body {
    tr {
      transition: var(--transition) !important;
      border-bottom: 1px solid var(--border-color) !important;

      &:hover {
        background: var(--primary-lighter) !important;
        transform: translateY(-1px) !important;
        box-shadow: var(--shadow-light) !important;
      }

      &.t-table__row--striped {
        background: var(--primary-lighter) !important;

        &:hover {
          background: var(--primary-light) !important;
        }
      }
    }

    td {
      border-bottom: 1px solid var(--border-color) !important;
      padding: 16px 12px !important;
      font-size: 0.875rem !important;
      color: var(--text-primary) !important;
    }
  }
}

:deep(.t-pagination) {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;

  .t-pagination__total {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
  }

  .t-pagination__prev,
  .t-pagination__next,
  .t-pagination__item {
    border-radius: var(--radius-small) !important;
    transition: var(--transition) !important;
    border: 1px solid var(--border-color) !important;
    background: var(--background-primary) !important;
    color: var(--text-primary) !important;
    font-weight: 500 !important;

    &:hover {
      background: var(--primary-color) !important;
      color: white !important;
      border-color: var(--primary-color) !important;
      transform: translateY(-1px) !important;
      box-shadow: var(--shadow-light) !important;
    }

    &.t-is-current {
      background: var(--primary-color) !important;
      color: white !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
    }
  }
}

:deep(.t-input) {
  border-radius: var(--radius-small) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  background: var(--background-primary) !important;
  box-shadow: var(--shadow-light) !important;
  height: 40px !important;
  font-size: 0.875rem !important;
  color: var(--text-primary) !important;

  &::placeholder {
    color: var(--text-secondary) !important;
    font-weight: 400 !important;
  }

  &:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  &:hover {
    border-color: var(--primary-border-hover) !important;
  }
}

:deep(.t-popconfirm) {
  .t-popup__content {
    border-radius: var(--radius-medium) !important;
    box-shadow: var(--shadow-heavy) !important;
  }
}
</style>
