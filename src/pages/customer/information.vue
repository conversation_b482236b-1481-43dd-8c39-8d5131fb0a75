<template>
  <div class="modern-list-container">
    <!-- 统计区域 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon"></div>
        <div class="stat-content">
          <div class="stat-number">{{list.length}}</div>
          <div class="stat-label">检查项目</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{normalCount}}</div>
          <div class="stat-label">正常项目</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{warningCount}}</div>
          <div class="stat-label">建议调整</div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="modern-table-container">
        <t-table
          rowKey="index"
          :data="list"
          :columns="columns"
          :stripe="true"
          :bordered="false"
          :hover="true"
          size="large"
          table-layout="auto"
          :showHeader="true"
          cellEmptyContent="-"
          class="modern-table"
        >
        <template #check="{ row }">
          <t-tag v-if="row.check==1" theme="success">正常</t-tag>
          <t-tag v-if="row.check==0" theme="warning">建议调整</t-tag>
          <t-tag v-if="row.check==3" theme="primary" style="cursor: pointer" @click="showMode = true">升级</t-tag>
        </template>
        </t-table>
      </div>
    </div>

    <t-dialog
      :visible="showMode"
      :header="!showUpdata?'权限验证':'版本文件上传'"
      confirmBtn="确定"
      cancelBtn="取消"
      :onConfirm="getCheck"
      :onClose="onClose"
      :closeOnOverlayClick="false"
      :closeOnEscKeydown="false"
    >
      <t-input-adornment prepend="二级密码" v-if="!showUpdata">
        <t-input v-model="pass" type="password" placeholder="请输入二级密码"></t-input>
      </t-input-adornment>
      <t-row v-if="showUpdata">
        <t-col :span="6" :offset="4">
          <t-upload style="margin: 0 auto"
                    action="/web/index.php?s=/cloud/Modules/UploadZip"
                    :headers="{token:$store.state.user.token}"
                    v-model="newFile"
                    theme="file"
                    accept=".zip"
                    :onSuccess="get_file"
          ></t-upload>
        </t-col>
      </t-row>
    </t-dialog>
  </div>

</template>
<script>
export default {
  data() {
    return {
      showMode: false,
      showUpdata: false,
      newFile: [],
      pass: '',
      list: [],
      columns: [
        {colKey: 'name', title: '系统信息', align: 'center'},
        {colKey: 'value', title: '参数', align: 'center'},
        {colKey: 'check', title: '当前状态', align: 'center'},
        {colKey: 'msg', title: '建议', align: 'center'},
      ]
    };
  },
  computed: {
    normalCount() {
      return this.list.filter(item => item.check === 1).length;
    },
    warningCount() {
      return this.list.filter(item => item.check === 0).length;
    },
    upgradeCount() {
      return this.list.filter(item => item.check === 3).length;
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    get_file(d) {
      var res = d.response;
      if (res.code == 200) {
        this.$message.success(res.msg);
      } else {
        this.$message.error(res.msg);
      }
      setTimeout(() => {
        this.showMode = false;
        this.showUpdata = false;
        window.location.reload();
      }, 1500)
    },
    onClose() {
      this.showMode = false;
    },
    getCheck() {
      if (this.showUpdata) {
        this.showMode = false;
        this.showUpdata = false;
        this.getList();
      } else {
        this.$request
          .post("/Settings/checkLimi", {pass: this.pass})
          .then((res) => {
            console.log(res);
            if (res.code == 200) {
              this.showUpdata = true;
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    getList() {
      this.$request
        .post("/Settings/check_info")
        .then((res) => {
          this.list = res.list;
        })
        .catch((e) => {
          console.log(e);
        });
    },

    onReset() {

    },
  },

};
</script>
<style lang="less">
// ==================== 现代化SaaS设计变量 ====================
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #f3f4f6;
  --primary-lighter: #f8f9fa;
  --primary-border: #e9ecef;
  --primary-border-hover: #d1d5db;
  --success-color: #10b981;
  --success-dark: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-form: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

<style lang="less" scoped>
// ==================== 现代化系统信息页面设计系统 ====================

// 页面容器现代化
.modern-list-container {
  min-height: 100vh !important;
  background: var(--background-primary);
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域现代化
.page-header {
  text-align: center !important;
  margin-bottom: 32px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 0 8px 0 !important;
    letter-spacing: -0.025em !important;

    @media (max-width: 768px) {
      font-size: 1.5rem !important;
    }
  }

  .page-subtitle {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 统计区域现代化
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

// 统计卡片现代化
.stat-card {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 24px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0.75%;
    right: 0.75%;
    height: 2px;
    width: 98.5%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-medium) var(--radius-medium) 0 0;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);

    &::before {
      opacity: 1;
    }
  }

  .stat-content {
    flex: 1;

    .stat-number {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 4px;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--text-secondary);
      font-weight: 500;
    }
  }
}

// 表格区域现代化
.table-section {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 32px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  transition: var(--transition);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-border-hover);
  }
}

// 现代化表格容器
.modern-table-container {
  .modern-table {
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

.stat-card {
  animation: bounceIn 0.6s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}

.table-section {
  animation: slideInUp 0.6s ease-out 0.5s both;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-list-container {
    padding: 20px 10px;
  }
}

// TDesign组件样式现代化覆盖
:deep(.t-table) {
  border-radius: var(--radius-medium) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-color) !important;
  background: var(--background-primary) !important;

  .t-table__header {
    background: var(--primary-lighter) !important;

    th {
      background: var(--primary-lighter) !important;
      color: var(--text-primary) !important;
      font-weight: 600 !important;
      font-size: 0.875rem !important;
      border-bottom: 1px solid var(--border-color) !important;
      padding: 16px 12px !important;
    }
  }

  .t-table__body {
    tr {
      transition: var(--transition) !important;
      border-bottom: 1px solid var(--border-color) !important;

      &:hover {
        background: var(--primary-lighter) !important;
        transform: translateY(-1px) !important;
        box-shadow: var(--shadow-light) !important;
      }

      &.t-table__row--striped {
        background: var(--primary-lighter) !important;

        &:hover {
          background: var(--primary-light) !important;
        }
      }
    }

    td {
      border-bottom: 1px solid var(--border-color) !important;
      padding: 16px 12px !important;
      font-size: 0.875rem !important;
      color: var(--text-primary) !important;
    }
  }
}

// 状态标签样式现代化
:deep(.t-tag) {
  border-radius: var(--radius-small) !important;
  padding: 4px 12px !important;
  font-weight: 600 !important;
  font-size: 0.75rem !important;
  border: none !important;
  transition: var(--transition) !important;
  letter-spacing: 0.025em !important;

  &.t-tag--success {
    background: var(--success-color) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2) !important;

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
    }
  }

  &.t-tag--warning {
    background: var(--warning-color) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(217, 119, 6, 0.2) !important;

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3) !important;
    }
  }

  &.t-tag--primary {
    background: var(--primary-color) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
    cursor: pointer !important;

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }
  }
}

// 弹窗样式现代化
:deep(.t-dialog) {
  border-radius: var(--radius-large) !important;
  box-shadow: var(--shadow-heavy) !important;

  .t-dialog__header {
    background: var(--primary-lighter) !important;
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    border-bottom: 1px solid var(--border-color) !important;
    border-radius: var(--radius-large) var(--radius-large) 0 0 !important;
  }

  .t-dialog__body {
    padding: 32px !important;
    background: var(--background-primary) !important;
  }

  .t-dialog__footer {
    background: #f8fbff !important;
    border-top: 1px solid #e3f2fd !important;
    padding: 20px 30px !important;

    .t-button {
      border-radius: 10px !important;
      font-weight: 600 !important;
      transition: all 0.3s ease !important;

      &:hover {
        transform: translateY(-2px) !important;
      }
    }
  }
}

// 输入框样式优化
:deep(.t-input-adornment) {
  border-radius: 10px !important;
  overflow: hidden !important;

  .t-input-adornment__prepend {
    background: #f8fbff !important;
    color: #1976D2 !important;
    font-weight: 600 !important;
    border-right: 2px solid #e3f2fd !important;
  }

  .t-input {
    border: none !important;
    background: #ffffff !important;

    &:focus {
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
    }
  }
}

// 上传组件样式优化
:deep(.t-upload) {
  .t-upload__dragger {
    border-radius: 12px !important;
    border: 2px dashed #e3f2fd !important;
    background: #f8fbff !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: #2196F3 !important;
      background: #f0f8ff !important;
    }
  }
}
</style>
