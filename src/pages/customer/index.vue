<template>
  <div class="modern-form-container">
    <t-form :data="formData" @reset="onReset" @submit="onSubmit" :label-width="0" class="modern-form">
      <!-- 第一步：联系方式 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">联系方式</h3>
          <p class="section-desc">设置客服的联系电话和QQ</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">客服电话</label>
              <t-input v-model="formData.phone" placeholder="请输入客服电话" class="modern-input" />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">客服QQ</label>
              <t-input v-model="formData.qq" placeholder="请填写客服QQ" class="modern-input" />
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：二维码配置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">二维码配置</h3>
          <p class="section-desc">上传客服二维码图片</p>
        </div>

        <div class="qr-grid">
          <div class="qr-group">
            <div class="custom-upload-container">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{ token: $store.state.user.token }"
                v-model="formData.qr_img"
                theme="custom"
                accept="image/*"
                :multiple="false"
                class="custom-upload"
              >
                <div class="upload-area">
                  <div v-if="!formData.qr_img || formData.qr_img.length === 0" class="upload-placeholder">
                    <div class="upload-text">点击上传二维码①</div>
                    <div class="upload-hint">支持 JPG、PNG 格式</div>
                  </div>
                  <div v-else class="upload-preview">
                    <img :src="formData.qr_img[0].url" alt="二维码①" class="preview-image">
                    <div class="upload-overlay">
                      <div class="overlay-text">点击重新上传</div>
                    </div>
                  </div>
                </div>
              </t-upload>
            </div>
          </div>

          <div class="qr-group">
            <div class="custom-upload-container">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{ token: $store.state.user.token }"
                v-model="formData.qr_img_tow"
                theme="custom"
                accept="image/*"
                :multiple="false"
                class="custom-upload"
              >
                <div class="upload-area">
                  <div v-if="!formData.qr_img_tow || formData.qr_img_tow.length === 0" class="upload-placeholder">
                    <div class="upload-text">点击上传二维码②</div>
                    <div class="upload-hint">支持 JPG、PNG 格式</div>
                  </div>
                  <div v-else class="upload-preview">
                    <img :src="formData.qr_img_tow[0].url" alt="二维码②" class="preview-image">
                    <div class="upload-overlay">
                      <div class="overlay-text">点击重新上传</div>
                    </div>
                  </div>
                </div>
              </t-upload>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button theme="primary" type="submit" class="action-btn submit-btn" size="large">
          <span>保存配置</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formData: {
        qr_img: [],
        qr_img_tow: [],
        phone: '',
        qq: '',
        status: false
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.$request
        .post("/Customer/getInfo",)
        .then((res) => {
          if (res.info.qr_img == '') {
            this.formData.qr_img = [];
          } else {
            this.formData.qr_img = [{ url: res.info.qr_img }];
          }
          if (res.info.qr_img_tow == '') {
            this.formData.qr_img_tow = [];
          } else {
            this.formData.qr_img_tow = [{ url: res.info.qr_img_tow }];
          }
          this.formData.phone = res.info.phone;
          this.formData.qq = res.info.qq;
          this.formData.status = 1;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({ validateResult }) {
      this.formData.status = 1;
      if (validateResult === true) {
        this.$request
          .post("/Customer/index", { from_data: this.formData })
          .then((res) => {
            if (res.code == 200) {
              this.getList();
              this.$message.success(res.msg);
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    onReset() {

    },
  },

};
</script>
<style lang="less">
// ==================== 现代化SaaS设计变量 ====================
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #f3f4f6;
  --primary-lighter: #f8f9fa;
  --primary-border: #e9ecef;
  --primary-border-hover: #d1d5db;
  --success-color: #10b981;
  --success-dark: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-form: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器现代化
.modern-form-container {
  min-height: 100vh !important;
  background: var(--background-primary);
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域现代化
.page-header {
  text-align: center !important;
  margin-bottom: 32px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 0 8px 0 !important;
    letter-spacing: -0.025em !important;

    @media (max-width: 768px) {
      font-size: 1.5rem !important;
    }
  }

  .page-subtitle {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块现代化
.form-section {
  background: var(--background-primary) !important;
  border-radius: var(--radius-large) !important;
  padding: 32px !important;
  margin-bottom: 24px !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  position: relative !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0.75%;
    right: 0.75%;
    height: 2px;
    width: 98.5%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-medium) var(--radius-medium) 0 0;
  }

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-heavy) !important;
    border-color: var(--primary-border-hover) !important;

    &::before {
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    padding: 20px !important;
  }
}

// 区块标题现代化
.section-header {
  margin-bottom: 24px;
  text-align: center;

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    letter-spacing: -0.025em;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// 表单网格
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 二维码网格 - 居中布局
.qr-grid {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
}

// 二维码组
.qr-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 250px;

  .modern-label {
    text-align: center;
  }

  .upload-container {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

// 表单组
.form-group {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

// 简化输入框设计
.floating-input {
  position: relative;
  margin-bottom: 0;
  width: 100%;

  .floating-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
  }

  .modern-input {
    width: 100%;
  }
}

// 上传容器
.upload-container {
  margin-top: 10px;

  .modern-upload {
    border-radius: 12px;
  }
}

// 自定义上传容器
.custom-upload-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 15px;

  .custom-upload {
    width: 200px;
    height: 200px;
  }
}

// 上传区域
.upload-area {
  width: 200px;
  height: 200px;
  border: 2px dashed #e3f2fd;
  border-radius: 15px;
  background: #f8fbff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #2196F3;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.15);
  }
}

// 上传占位符
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 20px;

  .upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #2196F3;
  }

  .upload-text {
    font-size: 1rem;
    font-weight: 600;
    color: #1976D2;
    margin-bottom: 8px;
  }

  .upload-hint {
    font-size: 0.8rem;
    color: #666;
  }
}

// 上传预览
.upload-preview {
  position: relative;
  width: 100%;
  height: 100%;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 13px;
  }

  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(33, 150, 243, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 13px;

    .overlay-text {
      color: white;
      font-weight: 600;
      font-size: 0.9rem;
    }
  }

  &:hover .upload-overlay {
    opacity: 1;
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em !important;

  .btn-icon {
    font-size: 1rem;
  }

  &.submit-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;

    &:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) {
    animation-delay: 0.1s;
  }

  &:nth-child(3) {
    animation-delay: 0.2s;
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式现代化覆盖
:deep(.t-input) {
  border-radius: var(--radius-small) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  background: var(--background-primary) !important;
  box-shadow: var(--shadow-light) !important;
  height: 48px !important;
  font-size: 0.875rem !important;
  color: var(--text-primary) !important;

  &::placeholder {
    color: var(--text-secondary) !important;
    font-weight: 400 !important;
  }

  &:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  &:hover {
    border-color: var(--primary-border-hover) !important;
  }

  .t-input__inner {
    height: 46px !important;
    line-height: 46px !important;
    color: var(--text-primary) !important;

    &::placeholder {
      color: var(--text-secondary) !important;
      font-weight: 400 !important;
    }
  }
}

:deep(.t-upload) {
  .t-upload__dragger {
    border-radius: var(--radius-medium) !important;
    border: 2px dashed var(--border-color) !important;
    transition: var(--transition) !important;
    background: var(--primary-lighter) !important;

    &:hover {
      border-color: var(--primary-color) !important;
      background: var(--primary-light) !important;
    }
  }

  .t-upload__file-list {
    margin-top: 15px;
  }
}

:deep(.t-upload__card) {
  justify-content: center;
}
</style>
