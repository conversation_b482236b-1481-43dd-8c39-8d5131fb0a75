
<template>
  <div class="modern-form-container">
    <t-form
      :data="formData"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
      <!-- 第一步：基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <p class="section-desc">修改公告的标题和基本信息</p>
        </div>

        <div class="form-grid">
          <div class="form-group full-width">
            <div class="floating-input">
              <t-input
                v-model="formData.title"
                placeholder="请输入公告标题"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">公告标题</label>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：内容编辑 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">内容编辑</h3>
          <p class="section-desc">修改公告的详细内容</p>
        </div>

        <div class="editor-container">
          <label class="modern-label">公告内容</label>
          <div class="modern-editor-wrapper">
            <!-- 工具栏 -->
            <Toolbar
              class="editor-toolbar"
              :mode="mode"
              :editor="editor"
              :defaultConfig="toolbarConfig"
            />

            <Editor
              class="editor-content"
              v-model="formData.content"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="(e) => onCreated(e)"
            />
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button
          theme="default"
          @click="()=>{this.$router.push('/customer/announcement')}"
          class="action-btn back-btn"
          size="large"
        >
          <span>返回列表</span>
        </t-button>
        <t-button
          theme="primary"
          type="submit"
          class="action-btn submit-btn"
          size="large"
        >
          <span>保存修改</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
export default {
  components: { Editor, Toolbar },
  data() {
    return {
      formData:{
        title:'',
        content:''
      },
      refreshKey: '',
      editor: null,
      toolbarConfig: {
        showLinkImg: false,
        uploadImgShowBase64: true,
        excludeKeys: [
          'insertVideo', // 删除视频
          'uploadVideo',
          'group-video',
          'group-image',
          'insertImage',// 删除网络图片上传
          'insertLink',// 删除链接
          'insertTable',// 删除表格
          'codeBlock',// 删除代码块
        ]
      },
      editorConfig: {
        placeholder: '',
        readOnly: false, // 是否只允许阅读，不可编辑
        autoFocus: true,
        MENU_CONF: {}
      },
      mode: 'default', // or 'simple'
      id:0,
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getList();
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    getList() {
      this.$request
        .post("/Settings/getEditAbout",{id:this.id})
        .then((res) => {
          this.formData.title = res.info.title;
          this.formData.content = res.info.content;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onCreated(editor) {
      this.editor = Object.seal(editor);
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Settings/EditAbout",{from_data:this.formData,id:this.id})
          .then((res) => {
            if(res.code==200){
              this.$message.success(res.msg);
              setTimeout(()=>{
                this.$router.push('announcement');
              },1500)
            }else{
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    openUrl(url){
      this.$router.push(url);
    }
  },

};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器
.modern-form-container {
  min-height: 100vh !important;
  background: #ffffff;
  padding: 40px 20px !important;
  position: relative !important;
  border-radius: 20px;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块
.form-section {
  background: #ffffff !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin-bottom: 30px !important;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08) !important;
  border: 1px solid #e3f2fd !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 28px rgba(33, 150, 243, 0.12) !important;
    border-color: #bbdefb !important;
  }

  @media (max-width: 768px) {
    padding: 25px !important;
  }
}

// 区块标题
.section-header {
  margin-bottom: 30px;
  text-align: center;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
  }
}

// 表单网格
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 表单组
.form-group {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 0.95rem;
}

// 浮动输入框
.floating-input {
  position: relative;

  .modern-input {
    width: 100%;
    height: 56px;
    border: 2px solid transparent;
    border-radius: var(--radius-small);
    padding: 20px 16px 8px 16px;
    font-size: 1rem;
    background: var(--background-primary);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }
    &:not(:placeholder-shown) + .floating-label,
    &:focus + .floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .floating-label {
    position: absolute !important;
    left: 0px !important;
    top: 40% !important;
    transform: translateY(-50%) !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 10 !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

// 编辑器容器
.editor-container {
  .modern-editor-wrapper {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-medium);
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:hover {
      border-color: var(--primary-border-hover);
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.08);
    }

    &:focus-within {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }

    .editor-toolbar {
      background: #f8fbff !important;
      border-bottom: 1px solid #e3f2fd !important;
      padding: 10px 15px !important;
    }

    .editor-content {
      height: 400px !important;
      background: #ffffff !important;
    }
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.submit-btn {
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

    &:hover {
      background: var(--primary-dark) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }

  &.back-btn {
    background: #f5f5f5 !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;

    &:hover {
      background: #eeeeee !important;
      color: #333 !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

// 富文本编辑器样式覆盖
:deep(.w-e-toolbar) {
  background: #f8fbff !important;
  border-bottom: 1px solid #e3f2fd !important;

  .w-e-bar-item {
    border-radius: 6px !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: #e3f2fd !important;
      color: #1976D2 !important;
    }

    &.w-e-bar-item-active {
      background: #2196F3 !important;
      color: white !important;
    }
  }
}

:deep(.w-e-text-container) {
  background: #ffffff !important;

  .w-e-text-placeholder {
    color: #999 !important;
    font-style: italic !important;
  }

  .w-e-text {
    line-height: 1.6 !important;
    font-size: 14px !important;
    color: #333 !important;

    p {
      margin: 8px 0 !important;
    }

    h1, h2, h3, h4, h5, h6 {
      color: #1976D2 !important;
      margin: 16px 0 8px 0 !important;
    }

    blockquote {
      border-left: 4px solid #2196F3 !important;
      background: #f8fbff !important;
      padding: 10px 15px !important;
      margin: 10px 0 !important;
    }

    code {
      background: #f0f8ff !important;
      color: #1976D2 !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
    }

    pre {
      background: #f8fbff !important;
      border: 1px solid #e3f2fd !important;
      border-radius: 8px !important;
      padding: 15px !important;
      margin: 10px 0 !important;
    }
  }
}
</style>
