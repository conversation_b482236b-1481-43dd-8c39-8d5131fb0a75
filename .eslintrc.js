module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true,
    jest: true, // 从原配置合并
  },
  extends: [
    'airbnb-base',
    'prettier',
    'plugin:@typescript-eslint/recommended',
    'plugin:vue/essential', // 使用原配置的更宽松设置
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2020,
    sourceType: 'module',
    allowImportExportEverywhere: true, // 从原配置合并
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ['vue', '@typescript-eslint'],
  settings: {
    'import/resolver': {
      alias: {
        map: [
          ['@', './src'],
        ],
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.vue'],
      },
    },
  },
  rules: {
    // 从原配置合并的TypeScript规则
    '@typescript-eslint/ban-ts-ignore': 0,
    '@typescript-eslint/no-explicit-any': 0,
    '@typescript-eslint/no-require-imports': 0,
    '@typescript-eslint/no-var-requires': 0,
    '@typescript-eslint/prefer-for-of': 0,
    '@typescript-eslint/explicit-function-return-type': 0,
    '@typescript-eslint/explicit-module-boundary-types': 0,

    // 从原配置合并的Import规则
    'import/no-extraneous-dependencies': 0,
    'import/extensions': 0,
    'import/no-unresolved': 0,

    // 从原配置合并的基础规则
    indent: [2, 2],
    camelcase: 0,
    'class-methods-use-this': 0,
    'new-cap': 0,
    'no-new': 1,
    'no-shadow': 0,
    'no-console': 0,
    'no-underscore-dangle': 0,
    'no-confusing-arrow': 0,
    'no-plusplus': ['error', { allowForLoopAfterthoughts: true }],
    'no-param-reassign': 0,
    'func-style': 0,
    'prefer-default-export': 0,
    'max-len': 0,
    'consistent-return': 0,

    // 关闭组件命名规则 - 解决你遇到的主要问题
    'vue/component-name-in-template-casing': 'off', // 这是关键！原配置中启用了这个规则
    'vue/component-definition-name-casing': 'off',
    'vue/require-default-prop': 'off',

    // 其他Vue规则调整
    'vue/name-property-casing': 'off',
    'vue/prop-name-casing': 'off',
    'vue/attribute-hyphenation': 'off',
    'vue/v-on-event-hyphenation': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/html-self-closing': 'off',
    'vue/html-closing-bracket-newline': 'off',
    'vue/require-prop-types': 'off',
    'vue/no-v-html': 'off',
    'vue/html-indent': 'off',
    'vue/script-indent': 'off',

    // CSS和样式相关规则 - 针对WebStorm
    'vue/no-parsing-error': 'off',
    'vue/valid-template-root': 'off',
    'vue/no-multiple-template-root': 'off',
  },
  overrides: [
    {
      files: ['*.vue'],
      rules: {
        // 从原配置合并，但关闭问题规则
        'vue/return-in-computed-property': 1,
        'vue/order-in-components': 2,
        'vue/component-name-in-template-casing': 'off', // 原配置启用了这个，现在关闭
        'vue/require-default-prop': 0,
        '@typescript-eslint/explicit-module-boundary-types': 'off',
        'import/order': 'off',
      },
    },
    {
      files: ['src/*', '*.js', '*.jsx'],
      rules: {
        // 从原配置合并
        'no-var-requires': 0,
        'no-console': 0,
        'no-unused-expressions': 0,
        '@typescript-eslint/explicit-module-boundary-types': 'off',
        '@typescript-eslint/no-var-requires': 'off',
        'import/order': 'off',
      },
    },
  ],
  globals: {
    // 从原配置合并
    cy: 'readonly',
    // Vue 3 全局变量定义
    defineProps: 'readonly',
    defineEmits: 'readonly',
    defineExpose: 'readonly',
    withDefaults: 'readonly',
  },
};
