module.exports = {
  defaultSeverity: 'error',
  extends: ['stylelint-config-prettier'],
  plugins: ['stylelint-less'],
  rules: {
    // 忽略未知的伪类选择器，包括 :deep
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep', 'global']
      }
    ],
    // 忽略未知的伪元素选择器
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep', 'deep']
      }
    ],
    // 关闭选择器类型检查（如果上面的规则不够用）
    'selector-type-no-unknown': null,
  },
};
